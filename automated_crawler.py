# automated_crawler.py
"""
全新的全自动化爬虫控制器
"""
import logging
import time
from read_cookie import ReadCookie
from batch_readnum_spider import BatchReadnumSpider
from excel_auto_crawler import ExcelAutoCrawler

class AutomatedCrawler:
    """
    协调整个自动化流程的控制器:
    1. 启动 mitmproxy 抓取器 (会自动设置代理).
    2. 运行 UI 自动化打开微信文章以触发抓取.
    3. 等待并验证 Cookie 是否成功抓取.
    4. 停止 mitmproxy 抓取器 (会自动关闭代理).
    5. 使用获取到的 Cookie 运行批量爬虫.
    """
    def __init__(self):
        self.logger = logging.getLogger()
        self.cookie_reader = ReadCookie()

    def run(self):
        """执行完整的自动化流程"""
        self.logger.info("="*80)
        self.logger.info("🚀 全新自动化流程启动 🚀")
        self.logger.info("="*80)

        try:
            # 1. 启动 mitmproxy 抓取器
            self.logger.info("[步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...")
            if not self.cookie_reader.start_cookie_extractor():
                self.logger.error("❌ Cookie 抓取器启动失败，流程中止。")
                return False
            self.logger.info("✅ Cookie 抓取器已在后台运行。")

            # 2. 运行 UI 自动化触发抓取
            self.logger.info("[步骤 2/5] 正在启动 UI 自动化以触发 Cookie 获取...")
            try:
                # 我们只需要UI自动化打开链接，不需要它执行后续的爬取
                ui_crawler = ExcelAutoCrawler()
                if not ui_crawler.open_wechat_and_trigger_url():
                     self.logger.error("❌ UI 自动化触发失败，流程中止。")
                     # 确保停止 mitmproxy
                     self.cookie_reader.stop_cookie_extractor()
                     return False
            except Exception as e:
                self.logger.error(f"❌ UI 自动化过程中发生错误: {e}")
                self.cookie_reader.stop_cookie_extractor() # 确保停止 mitmproxy
                return False
            self.logger.info("✅ UI 自动化已成功触发链接打开。")

            # 3. 等待并验证 Cookie
            self.logger.info("[步骤 3/5] 正在等待 Cookie 数据写入...")
            if not self.cookie_reader.wait_for_new_cookie(timeout=120): # 2分钟超时
                self.logger.error("❌ 等待 Cookie 超时，流程中止。")
                self.cookie_reader.stop_cookie_extractor() # 确保停止 mitmproxy
                return False
            
            # 验证一下cookie是否真的有效
            auth_info = self.cookie_reader.get_latest_cookies()
            if not auth_info:
                self.logger.error("❌ 虽然检测到文件，但未能解析出有效的Cookie，流程中止。")
                self.cookie_reader.stop_cookie_extractor()
                return False
            self.logger.info("✅ 成功获取并验证了新的 Cookie。")

            # 4. 停止 mitmproxy 抓取器 (关闭代理)
            self.logger.info("[步骤 4/5] 正在停止 Cookie 抓取器并关闭系统代理...")
            self.cookie_reader.stop_cookie_extractor()
            # 等待几秒确保代理完全关闭
            time.sleep(3)
            self.logger.info("✅ Cookie 抓取器已停止，系统代理已恢复。")

            # 5. 运行批量爬虫
            self.logger.info("[步骤 5/5] 正在启动批量文章爬取...")
            batch_spider = BatchReadnumSpider(auth_info=auth_info)
            batch_spider.batch_crawl_readnum()
            
            if batch_spider.articles_data:
                batch_spider.print_summary()
                excel_file = batch_spider.save_to_excel()
                json_file = batch_spider.save_to_json()
                self.logger.info(f"🎉 抓取完成！结果已保存到 {excel_file} 和 {json_file}")
            else:
                self.logger.warning("⚠️ 未抓取到任何文章数据，请检查公众号近期是否发文或Cookie是否依然有效。")

            self.logger.info("="*80)
            self.logger.info("✅ 全新自动化流程执行完毕 ✅")
            self.logger.info("="*80)
            return True

        except Exception as e:
            self.logger.error(f"❌ 自动化流程发生未知严重错误: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            # 确保在任何异常情况下都尝试停止 mitmproxy
            self.logger.info("正在尝试清理资源...")
            self.cookie_reader.stop_cookie_extractor()
            return False

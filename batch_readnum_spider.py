#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信公众号批量阅读量抓取器
基于现有代码架构，整合文章链接获取和阅读量抓取功能
"""

import os
import re
import json
import time
import random
import requests
import pandas as pd
import winreg
import ctypes
import contextlib
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
from read_cookie import ReadCookie
import utils

class BatchReadnumSpider:
    """批量微信公众号阅读量抓取器"""
    
    def __init__(self, auth_info: dict = None):
        """
        初始化批量阅读量抓取器
        :param auth_info: 包含appmsg_token, biz, cookie_str和headers的字典
        """
        # 初始化认证信息
        self.appmsg_token = None
        self.biz = None
        self.cookie_str = None
        self.auth_info = auth_info # 存储传入的认证数据

        # 请求头配置 - 参考spider_readnum.py的成功实现
        self.headers = {
            'cache-control': 'max-age=0',
            'x-wechat-key': '',
            'x-wechat-uin': '',
            'exportkey': '',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090c37) XWEB/14315 Flue',
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/wxpic,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-dest': 'document',
            'accept-encoding': 'gzip, deflate, br',
            'accept-language': 'zh-CN,zh;q=0.9',
            'priority': 'u=0, i',
        }

        # 从传入的auth_info加载认证信息和更新headers
        if self.auth_info:
            self.load_auth_info()
        else:
            print("❌ BatchReadnumSpider 初始化时未提供认证数据。")

        # 数据存储 - 统一存储所有字段
        self.articles_data = []

        # 频率控制
        self.request_count = 0
        self.last_request_time = 0
        self.min_interval = 3  # 最小请求间隔（秒）

        # 创建数据目录
        os.makedirs("./data/readnum_batch", exist_ok=True)
        
    def load_auth_info(self):
        """从传入的认证数据加载认证信息和headers"""
        if not self.auth_info:
            print("❌ 未传入有效的认证数据，无法加载认证信息。")
            return False

        try:
            self.appmsg_token = self.auth_info.get('appmsg_token')
            self.biz = self.auth_info.get('biz')
            self.cookie_str = self.auth_info.get('cookie_str')

            # 更新请求头，使用抓包获取的真实headers
            captured_headers = self.auth_info.get('headers', {})
            if captured_headers:
                # 使用抓包获取的所有headers覆盖默认值
                # 特别重要的是x-wechat-key，这是获取阅读量的关键
                for key, value in captured_headers.items():
                    self.headers[key] = value

                # 检查关键的headers是否存在
                key_headers = ['x-wechat-key', 'x-wechat-uin', 'exportkey']
                missing_headers = [h for h in key_headers if h not in captured_headers]

                if missing_headers:
                    print(f"⚠️ 缺少关键headers: {missing_headers}")
                    # 如果缺少x-wechat-key，使用默认值（来自spider_readnum.py的成功实现）
                    if 'x-wechat-key' in missing_headers:
                        print("🔑 使用默认的x-wechat-key值")
                else:
                    print(f"✅ 已更新所有 {len(captured_headers)} 个请求头参数，包含关键的x-wechat-key")

                # 显示x-wechat-key的前20个字符用于验证
                if 'x-wechat-key' in captured_headers:
                    print(f"🔑 x-wechat-key: {captured_headers['x-wechat-key'][:20]}...")
                elif 'x-wechat-key' in self.headers:
                    print(f"🔑 使用默认x-wechat-key: {self.headers['x-wechat-key'][:20]}...")
            else:
                print("⚠️ 未获取到headers信息，使用默认的x-wechat-key")
                print(f"🔑 默认x-wechat-key: {self.headers['x-wechat-key'][:20]}...")

            print(f"✅ 成功加载认证信息")
            print(f"   __biz: {self.biz}")
            print(f"   appmsg_token: {self.appmsg_token[:20]}...")
            print(f"   headers: {list(captured_headers.keys())}")
            return True
        except Exception as e:
            print(f"❌ 加载认证信息失败: {e}")
            return False


    
    @contextlib.contextmanager
    def manage_system_proxy(self, proxy_address="127.0.0.1:8080"):
        """
        代理管理上下文管理器，临时禁用系统代理
        """
        INTERNET_OPTION_SETTINGS_CHANGED = 39
        INTERNET_OPTION_REFRESH = 37
        InternetSetOption = ctypes.windll.wininet.InternetSetOptionW
        
        original_state = {"enabled": False, "server": ""}
        was_active = False
        key = None
        
        try:
            # 打开注册表项
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                               r"Software\Microsoft\Windows\CurrentVersion\Internet Settings", 
                               0, winreg.KEY_READ | winreg.KEY_WRITE)
            
            # 读取原始代理状态
            try:
                original_state["enabled"] = winreg.QueryValueEx(key, "ProxyEnable")[0] == 1
                original_state["server"] = winreg.QueryValueEx(key, "ProxyServer")[0]
            except FileNotFoundError:
                pass
            
            # 检查代理是否是我们需要禁用的那个
            if original_state["enabled"] and original_state["server"] == proxy_address:
                was_active = True
                print(f"🔧 检测到活动代理 {proxy_address}，正在临时禁用...")
                winreg.SetValueEx(key, "ProxyEnable", 0, winreg.REG_DWORD, 0)
                InternetSetOption(0, INTERNET_OPTION_SETTINGS_CHANGED, 0, 0)
                InternetSetOption(0, INTERNET_OPTION_REFRESH, 0, 0)
            
            yield  # 执行主代码块
            
        finally:
            # 恢复原始代理设置
            if was_active and key:
                print(f"🔧 正在恢复代理 {proxy_address}...")
                winreg.SetValueEx(key, "ProxyEnable", 0, winreg.REG_DWORD, 1)
                InternetSetOption(0, INTERNET_OPTION_SETTINGS_CHANGED, 0, 0)
                InternetSetOption(0, INTERNET_OPTION_REFRESH, 0, 0)
            if key:
                winreg.CloseKey(key)
    
    def rate_limit(self):
        """智能频率控制"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_interval:
            sleep_time = self.min_interval - time_since_last
            print(f"⏳ 频率控制：等待 {sleep_time:.1f} 秒...")
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
        self.request_count += 1
        
        # 每10个请求增加额外延迟
        if self.request_count % 10 == 0:
            extra_delay = random.randint(5, 10)
            print(f"⏳ 第{self.request_count}个请求，额外延迟 {extra_delay} 秒...")
            time.sleep(extra_delay)
    
    def get_article_list(self, begin_page=0, count=10):
        """
        获取文章列表
        :param begin_page: 起始页数
        :param count: 每页文章数量
        :return: 文章列表
        """
        if not all([self.appmsg_token, self.biz, self.cookie_str]):
            print("❌ 认证信息不完整，无法获取文章列表")
            return []
        
        # 频率控制
        self.rate_limit()
        
        # 构建请求URL
        page_url = "https://mp.weixin.qq.com/mp/profile_ext"
        params = {
            "action": "getmsg",
            "__biz": self.biz,
            "f": "json",
            "offset": begin_page * count,
            "count": count,
            "is_ok": 1,
            "scene": "",
            "uin": "777",
            "key": "777",
            "pass_ticket": "",
            "wxtoken": "",
            "appmsg_token": self.appmsg_token,
            "x5": 0
        }
        
        # 解析cookie
        clean_cookie = self.cookie_str.replace('\u00a0', ' ').strip()
        cookie_dict = utils.str_to_dict(clean_cookie, join_symbol='; ', split_symbol='=')
        
        if 'pass_ticket' in cookie_dict:
            params['pass_ticket'] = cookie_dict['pass_ticket']
        
        # 更新请求头
        headers = self.headers.copy()
        headers["Cookie"] = self.cookie_str
        
        try:
            print(f"📡 获取文章列表：第{begin_page+1}页，每页{count}篇")
            
            response = requests.get(page_url, params=params, headers=headers, verify=False, timeout=30)
            
            if response.status_code != 200:
                print(f"❌ 请求失败，状态码: {response.status_code}")
                return []
            
            # 解析响应
            try:
                content_json = response.json()
            except:
                print("❌ 响应不是有效的JSON格式")
                return []
            
            # 检查是否有错误
            if "base_resp" in content_json:
                base_resp = content_json["base_resp"]
                if base_resp.get("err_msg") == "freq control":
                    print("⚠️ 遇到频率控制限制，建议稍后重试")
                    return []
            
            # 解析文章列表
            if "general_msg_list" not in content_json:
                print("❌ 响应中没有找到文章列表")
                return []
            
            articles_json = json.loads(content_json["general_msg_list"])
            articles = []
            
            for item in articles_json.get("list", []):
                # 处理主文章
                if "app_msg_ext_info" in item and item["app_msg_ext_info"].get("content_url"):
                    main_article = item["app_msg_ext_info"]
                    articles.append({
                        "title": main_article.get("title", ""),
                        "url": main_article.get("content_url", ""),
                        "author": main_article.get("author", ""),
                        "digest": main_article.get("digest", ""),
                        "create_time": item.get("comm_msg_info", {}).get("datetime", 0)
                    })
                
                # 处理副文章
                if "app_msg_ext_info" in item:
                    for sub_article in item["app_msg_ext_info"].get("multi_app_msg_item_list", []):
                        articles.append({
                            "title": sub_article.get("title", ""),
                            "url": sub_article.get("content_url", ""),
                            "author": sub_article.get("author", ""),
                            "digest": sub_article.get("digest", ""),
                            "create_time": item.get("comm_msg_info", {}).get("datetime", 0)
                        })
            
            print(f"✅ 成功获取 {len(articles)} 篇文章")
            return articles
            
        except Exception as e:
            print(f"❌ 获取文章列表失败: {e}")
            return []

    def extract_article_content_and_stats(self, article_url):
        """
        从文章页面提取文章内容、阅读量、点赞数等统计信息
        参考spider_readnum.py的成功实现
        :param article_url: 文章URL
        :return: 包含内容和统计信息的字典
        """
        if not article_url:
            return None

        # 频率控制
        self.rate_limit()

        try:
            print(f"📊 抓取统计数据: {article_url[:50]}...")

            # 修复HTML编码的URL
            import html
            clean_url = html.unescape(article_url)
            print(f"🔍 清理后URL: {clean_url}")

            # 解析URL获取参数
            from urllib.parse import urlparse, parse_qs
            parsed_url = urlparse(clean_url)
            query_params = parse_qs(parsed_url.query)

            print(f"🔍 解析到的参数: {query_params}")

            # 构建请求参数，参考spider_readnum.py的成功实现
            # 注意：spider_readnum.py中参数都是列表格式
            params = {}
            for key in ['__biz', 'mid', 'idx', 'sn', 'chksm']:
                if key in query_params:
                    params[key] = query_params[key]  # parse_qs已经返回列表格式

            # 添加必要的参数
            # 从cookie中提取pass_ticket
            pass_ticket_match = re.search(r'pass_ticket=([^;]+)', self.cookie_str)
            if pass_ticket_match:
                params['pass_ticket'] = [pass_ticket_match.group(1)]

            params['wx_header'] = ['1']

            print(f"🔍 请求参数: {params}")

            # 使用实例的headers（已经包含了抓包获取的关键参数）
            headers = self.headers.copy()

            print(f"🔍 使用headers: {list(headers.keys())}")

            # 验证关键的x-wechat-key是否存在
            if 'x-wechat-key' in headers:
                print(f"🔑 确认x-wechat-key存在: {headers['x-wechat-key'][:20]}...")
            else:
                print("❌ 警告：x-wechat-key不存在，可能无法获取阅读量数据")

            # 添加Cookie
            headers['Cookie'] = self.cookie_str

            # 使用代理管理器临时禁用系统代理
            with self.manage_system_proxy("127.0.0.1:8080"):
                # 使用GET请求访问文章页面
                base_url = "https://mp.weixin.qq.com/s"
                response = requests.get(base_url, params=params, headers=headers, timeout=30)

                if response.status_code != 200:
                    print(f"❌ 文章请求失败，状态码: {response.status_code}")
                    return None

                html_content = response.text

                # 调试：保存HTML内容用于分析
                debug_dir = "./debug"
                os.makedirs(debug_dir, exist_ok=True)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                html_file = os.path.join(debug_dir, f"article_content_{timestamp}.html")

                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                print(f"🔍 HTML内容已保存到: {html_file}")
                print(f"📏 HTML长度: {len(html_content)} 字符")

                # 检查是否遇到验证码页面
                if "环境异常" in html_content or "完成验证" in html_content or "secitptpage/verify" in html_content:
                    print("⚠️ 遇到微信验证码页面，需要手动验证")
                    print(f"📄 请手动在浏览器中访问: {article_url}")
                    print("💡 建议：降低抓取频率，增加延迟时间")
                    return {
                        'read_count': -1,  # 用-1表示验证码页面
                        'like_count': -1,
                        'share_count': -1,
                        'error': 'captcha_required'
                    }

                # 检查是否为真实文章页面
                if "js_content" not in html_content and "rich_media_content" not in html_content:
                    print("⚠️ 非文章页面，可能被重定向或文章不存在")
                    return {
                        'read_count': -2,  # 用-2表示非文章页面
                        'like_count': -2,
                        'share_count': -2,
                        'error': 'not_article_page'
                    }

                # 提取文章基本信息
                title_match = re.search(r'<meta property="og:title" content="(.*?)"', html_content)

                title = title_match.group(1) if title_match else "未找到标题"

                # 提取文章内容
                content = self.extract_article_content(html_content)

                # 提取发布时间
                publish_time = self.extract_publish_time(html_content)

                # 构建完整的文章数据，包含内容和统计信息
                article_data = {
                    "title": title.strip(),
                    "url": article_url,
                    "content": content,
                    "publish_time": publish_time,
                    "read_count": 0,
                    "like_count": 0,
                    "old_like_count": 0,
                    "share_count": 0,
                    "comment_count": 0,
                    "crawl_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }

                # 使用spider_readnum.py中验证成功的正则表达式提取统计数据

                # 提取阅读量 - 使用成功验证的模式
                read_num_match = re.search(r"var cgiData = {[^}]*?read_num: '(\d+)'", html_content)
                read_count = int(read_num_match.group(1)) if read_num_match else 0

                if read_count > 0:
                    print(f"🔍 阅读量: {read_count}")
                else:
                    print("⚠️ 未找到阅读量数据，可能该文章未公开显示阅读量")

                article_data["read_count"] = read_count

                # 提取点赞数 - 使用成功验证的模式
                like_num_match = re.search(r"window\.appmsg_bar_data = {[^}]*?like_count: '(\d+)'", html_content)
                like_count = int(like_num_match.group(1)) if like_num_match else 0

                if like_count > 0:
                    print(f"🔍 点赞数: {like_count}")
                else:
                    print("⚠️ 未找到点赞数据")

                article_data["like_count"] = like_count

                # 提取历史点赞数 - 使用成功验证的模式
                old_like_num_match = re.search(r"window\.appmsg_bar_data = {[^}]*?old_like_count: '(\d+)'", html_content)
                old_like_count = int(old_like_num_match.group(1)) if old_like_num_match else 0

                if old_like_count > 0:
                    print(f"🔍 历史点赞数: {old_like_count}")

                article_data["old_like_count"] = old_like_count

                # 提取分享数 - 使用成功验证的模式
                share_count_match = re.search(r"window\.appmsg_bar_data = {[^}]*?share_count: '(\d+)'", html_content)
                share_count = int(share_count_match.group(1)) if share_count_match else 0

                if share_count > 0:
                    print(f"🔍 分享数: {share_count}")

                article_data["share_count"] = share_count

                print(f"✅ 统计数据: 阅读{article_data['read_count']} 点赞{article_data['like_count']} 分享{article_data['share_count']}")
                return article_data

        except Exception as e:
            print(f"❌ 提取统计数据失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def extract_article_content(self, html_content):
        """
        从HTML中提取文章正文内容
        :param html_content: HTML内容
        :return: 文章正文
        """
        try:
            # 方法1: 使用BeautifulSoup更准确地提取内容
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')

            # 尝试多种方式提取文章内容
            content_div = None

            # 优先尝试id="js_content"
            content_div = soup.find('div', {'id': 'js_content'})
            if not content_div:
                # 尝试class="rich_media_content"
                content_div = soup.find('div', {'class': 'rich_media_content'})
            if not content_div:
                # 尝试包含rich_media_content的class
                content_div = soup.find('div', class_=lambda x: x and 'rich_media_content' in x)

            if content_div:
                # 移除脚本和样式标签
                for script in content_div(["script", "style"]):
                    script.decompose()

                # 获取纯文本内容
                content_text = content_div.get_text(separator='\n', strip=True)

                # 清理多余的空行
                content_text = re.sub(r'\n\s*\n', '\n\n', content_text)
                content_text = content_text.strip()

                if content_text:
                    print(f"✅ 成功提取文章内容，长度: {len(content_text)} 字符")
                    return content_text

            # 方法2: 如果BeautifulSoup失败，使用spider_readnum.py中验证成功的正则表达式方法
            print("🔄 尝试使用正则表达式方法提取内容...")
            content_match = re.search(r'id="js_content".*?>(.*?)</div>', html_content, re.S)
            if content_match:
                # 简单清理HTML标签
                content = re.sub(r'<.*?>', '', content_match.group(1))
                content = content.strip()
                if content:
                    print(f"✅ 正则表达式方法成功提取内容，长度: {len(content)} 字符")
                    return content

            print("⚠️ 未找到文章内容")
            return "未找到文章内容"

        except Exception as e:
            print(f"⚠️ 提取文章内容失败: {e}")
            return "提取内容失败"

    def extract_publish_time(self, html_content):
        """
        从HTML中提取文章发布时间
        :param html_content: HTML内容
        :return: 发布时间
        """
        try:
            # 尝试多种方式提取发布时间
            time_patterns = [
                r'<em class="rich_media_meta rich_media_meta_text"[^>]*>(\d{4}-\d{2}-\d{2})</em>',
                r'<span class="rich_media_meta rich_media_meta_text"[^>]*>(\d{4}-\d{2}-\d{2})</span>',
                r'var publish_time = "(\d{4}-\d{2}-\d{2})"',
                r'"publish_time":"(\d{4}-\d{2}-\d{2})"'
            ]

            for pattern in time_patterns:
                match = re.search(pattern, html_content)
                if match:
                    return match.group(1)

            return "未找到发布时间"

        except Exception as e:
            print(f"⚠️ 提取发布时间失败: {e}")
            return "提取时间失败"

    def clean_html_content(self, html_content):
        """
        清理HTML内容，保留基本文本
        :param html_content: HTML内容
        :return: 清理后的文本
        """
        try:
            import re

            # 移除script和style标签
            html_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL)
            html_content = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.DOTALL)

            # 保留段落结构，将p标签替换为换行
            html_content = re.sub(r'<p[^>]*>', '\n', html_content)
            html_content = re.sub(r'</p>', '\n', html_content)

            # 保留换行标签
            html_content = re.sub(r'<br[^>]*>', '\n', html_content)

            # 移除其他HTML标签
            html_content = re.sub(r'<[^>]+>', '', html_content)

            # 清理多余的空白字符
            html_content = re.sub(r'\n\s*\n', '\n\n', html_content)
            html_content = html_content.strip()

            return html_content

        except Exception as e:
            print(f"⚠️ 清理HTML内容失败: {e}")
            return html_content

    def batch_crawl_readnum(self, max_pages=5, articles_per_page=10, days_back=10):
        """
        批量抓取文章阅读量
        :param max_pages: 最大页数
        :param articles_per_page: 每页文章数
        :param days_back: 抓取多少天内的文章
        :return: 抓取结果列表
        """
        print(f"🚀 开始批量抓取阅读量数据")
        print(f"📋 参数: 最大{max_pages}页，每页{articles_per_page}篇，{days_back}天内文章")

        if not self.load_auth_info():
            print("❌ 认证信息加载失败，无法继续")
            return []

        all_results = []
        cutoff_date = datetime.now() - timedelta(days=days_back)

        for page in range(max_pages):
            print(f"\n{'='*50}")
            print(f"📄 处理第 {page+1}/{max_pages} 页")

            # 获取文章列表
            articles = self.get_article_list(begin_page=page, count=articles_per_page)

            if not articles:
                print("❌ 未获取到文章，停止抓取")
                break

            page_results = []
            outdated_count = 0

            for i, article in enumerate(articles):
                print(f"\n📖 处理文章 {i+1}/{len(articles)}: {article['title'][:30]}...")

                # 检查文章时间
                if article['create_time']:
                    try:
                        article_date = datetime.fromtimestamp(article['create_time'])
                        if article_date < cutoff_date:
                            print(f"⏰ 文章超出时间范围，跳过")
                            outdated_count += 1
                            continue
                    except:
                        pass

                # 抓取文章内容和统计数据
                article_data = self.extract_article_content_and_stats(article['url'])

                if article_data:
                    # 检查是否遇到验证码
                    if article_data.get('error') == 'captcha_required':
                        print(f"🛑 遇到验证码，停止批量抓取")
                        print(f"💡 建议：手动完成验证后重新运行，或降低抓取频率")
                        break

                    # 检查是否为非文章页面
                    elif article_data.get('error') == 'not_article_page':
                        print(f"⚠️ 非文章页面，跳过")
                        continue

                    # 正常的统计数据
                    else:
                        # 合并文章信息和统计数据
                        result = {
                            **article,
                            **article_data,
                            "pub_time": datetime.fromtimestamp(article['create_time']).strftime("%Y-%m-%d %H:%M:%S") if article['create_time'] else ""
                        }
                        page_results.append(result)
                        all_results.append(result)

                        print(f"✅ 完成 {len(all_results)} 篇文章")
                else:
                    print(f"❌ 统计数据获取失败")

                # 文章间延迟
                if i < len(articles) - 1:
                    delay = random.randint(5, 8)
                    print(f"⏳ 文章间延迟 {delay} 秒...")
                    time.sleep(delay)

            print(f"📊 本页完成 {len(page_results)} 篇文章，超时 {outdated_count} 篇")

            # 如果本页大部分文章都超时，停止抓取
            if outdated_count > len(articles) * 0.7:
                print("🛑 大部分文章超出时间范围，停止抓取")
                break

            # 页面间延迟
            if page < max_pages - 1:
                page_delay = random.randint(10, 20)
                print(f"⏳ 页面间延迟 {page_delay} 秒...")
                time.sleep(page_delay)

        self.articles_data = all_results
        print(f"\n🎉 批量抓取完成！共获取 {len(all_results)} 篇文章的统计数据")

        return all_results

    def save_to_excel(self, filename=None):
        """
        保存数据到Excel文件
        :param filename: 文件名，如果为None则自动生成
        :return: 保存的文件路径
        """
        if not self.articles_data:
            print("⚠️ 没有数据可保存")
            return None

        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"./data/readnum_batch/readnum_batch_{timestamp}.xlsx"

        try:
            # 准备Excel数据 - 包含全部字段（不包含作者）
            excel_data = []
            for article in self.articles_data:
                excel_data.append({
                    '标题': article.get('title', ''),
                    '文章内容': article.get('content', ''),
                    '发布时间': article.get('publish_time', '') or article.get('pub_time', ''),
                    '阅读量': article.get('read_count', 0),
                    '点赞数': article.get('like_count', 0),
                    '历史点赞数': article.get('old_like_count', 0),
                    '分享数': article.get('share_count', 0),
                    '评论数': article.get('comment_count', 0),
                    '文章链接': article.get('url', ''),
                    '摘要': article.get('digest', ''),
                    '抓取时间': article.get('crawl_time', '')
                })

            # 创建DataFrame并保存
            df = pd.DataFrame(excel_data)

            # 确保目录存在
            os.makedirs(os.path.dirname(filename), exist_ok=True)

            # 保存到Excel
            df.to_excel(filename, index=False, engine='openpyxl')

            print(f"📊 Excel数据已保存到: {filename}")
            print(f"📈 共保存 {len(excel_data)} 条记录")

            return filename

        except Exception as e:
            print(f"❌ 保存Excel文件失败: {e}")
            return None

    def save_to_json(self, filename=None):
        """
        保存数据到JSON文件
        :param filename: 文件名，如果为None则自动生成
        :return: 保存的文件路径
        """
        if not self.articles_data:
            print("⚠️ 没有数据可保存")
            return None

        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"./data/readnum_batch/readnum_batch_{timestamp}.json"

        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(filename), exist_ok=True)

            # 准备JSON数据 - 去掉作者字段
            json_data = []
            for article in self.articles_data:
                # 复制文章数据但去掉作者字段
                clean_article = {k: v for k, v in article.items() if k != 'author'}
                json_data.append(clean_article)

            # 保存到JSON
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)

            print(f"💾 JSON数据已保存到: {filename}")
            print(f"📈 共保存 {len(self.articles_data)} 条记录")

            return filename

        except Exception as e:
            print(f"❌ 保存JSON文件失败: {e}")
            return None

    def generate_summary_report(self):
        """
        生成统计摘要报告
        :return: 摘要信息字典
        """
        if not self.articles_data:
            return None

        total_articles = len(self.articles_data)
        total_reads = sum(article.get('read_count', 0) for article in self.articles_data)
        total_likes = sum(article.get('like_count', 0) for article in self.articles_data)
        total_shares = sum(article.get('share_count', 0) for article in self.articles_data)

        avg_reads = total_reads / total_articles if total_articles > 0 else 0
        avg_likes = total_likes / total_articles if total_articles > 0 else 0
        avg_shares = total_shares / total_articles if total_articles > 0 else 0

        # 找出阅读量最高的文章
        top_article = max(self.articles_data, key=lambda x: x.get('read_count', 0))

        summary = {
            "total_articles": total_articles,
            "total_reads": total_reads,
            "total_likes": total_likes,
            "total_shares": total_shares,
            "avg_reads": round(avg_reads, 2),
            "avg_likes": round(avg_likes, 2),
            "avg_shares": round(avg_shares, 2),
            "top_article": {
                "title": top_article.get('title', ''),
                "read_count": top_article.get('read_count', 0),
                "like_count": top_article.get('like_count', 0),
                "url": top_article.get('url', '')
            },
            "crawl_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        return summary

    def print_summary(self):
        """打印统计摘要"""
        summary = self.generate_summary_report()
        if not summary:
            print("⚠️ 没有数据可统计")
            return

        print(f"\n{'='*60}")
        print(f"📊 批量阅读量抓取统计摘要")
        print(f"{'='*60}")
        print(f"📖 总文章数: {summary['total_articles']}")
        print(f"👀 总阅读量: {summary['total_reads']:,}")
        print(f"👍 总点赞数: {summary['total_likes']:,}")
        print(f"📤 总分享数: {summary['total_shares']:,}")
        print(f"📊 平均阅读量: {summary['avg_reads']:,.2f}")
        print(f"📊 平均点赞数: {summary['avg_likes']:.2f}")
        print(f"📊 平均分享数: {summary['avg_shares']:.2f}")
        print(f"\n🏆 阅读量最高文章:")
        print(f"   标题: {summary['top_article']['title']}")
        print(f"   阅读量: {summary['top_article']['read_count']:,}")
        print(f"   点赞数: {summary['top_article']['like_count']:,}")
        print(f"\n⏰ 统计时间: {summary['crawl_time']}")
        print(f"{'='*60}")


def main():
    """主函数示例"""
    print("🚀 微信公众号批量阅读量抓取器")
    print("="*50)

    # 初始化爬虫
    spider = BatchReadnumSpider()

    try:
        # 批量抓取阅读量（最近7天，最多3页，每页5篇）
        results = spider.batch_crawl_readnum(max_pages=3, articles_per_page=5, days_back=7)

        if results:
            # 打印统计摘要
            spider.print_summary()

            # 保存数据
            excel_file = spider.save_to_excel()
            json_file = spider.save_to_json()

            print(f"\n✅ 抓取完成！")
            if excel_file:
                print(f"📊 Excel文件: {excel_file}")
            if json_file:
                print(f"💾 JSON文件: {json_file}")
        else:
            print("❌ 未获取到任何数据")

    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

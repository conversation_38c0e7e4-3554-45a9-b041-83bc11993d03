# coding:utf-8
# excel_auto_crawler.py
import pandas as pd
import logging
import time
import os
from read_cookie import Read<PERSON><PERSON><PERSON>
from batch_readnum_spider import Batch<PERSON><PERSON><PERSON>Spider
from wechat_browser_automation import WeChatBrowserAutomation, UI_AUTOMATION_AVAILABLE

class ExcelAutoCrawler:
    def __init__(self, excel_path="target_articles.xlsx"):
        self.excel_path = excel_path
        self.logger = logging.getLogger()
        
        if not UI_AUTOMATION_AVAILABLE:
            raise ImportError("UI自动化库 'uiautomation' 未安装或导入失败。")
            
        self.automation = WeChatBrowserAutomation()
        # 使用旧版ReadCookie
        self.cookie_reader = ReadCookie(outfile="wechat_keys.txt")
        self.spider = BatchReadnumSpider()

    def _get_target_url_from_excel(self) -> str:
        # ... (此方法与之前版本相同，无需修改)
        self.logger.info(f"正在从 {self.excel_path} 读取目标URL...")
        if not os.path.exists(self.excel_path):
            self.logger.error(f"Excel文件未找到: {self.excel_path}")
            return None
        try:
            df = pd.read_excel(self.excel_path)
            url_column = '文章链接' if '文章链接' in df.columns else 'url'
            if url_column not in df.columns:
                self.logger.error("Excel中未找到 '文章链接' 或 'url' 列。")
                return None
            for url in df[url_column]:
                if pd.notna(url) and 'mp.weixin.qq.com' in str(url):
                    self.logger.info(f"成功读取到目标URL: {url[:50]}...")
                    return str(url)
            self.logger.error("Excel中未找到任何有效的微信文章链接。")
            return None
        except Exception as e:
            self.logger.error(f"读取Excel文件失败: {e}")
            return None

    def _get_new_cookie_via_automation(self) -> bool:
        """
        核心流程：启动抓取器，UI自动化打开链接，等待Cookie，停止抓取器。
        """
        target_url = self._get_target_url_from_excel()
        if not target_url:
            return False

        self.logger.info("启动mitmproxy抓取器...")
        if not self.cookie_reader.start_cookie_extractor():
            self.logger.error("mitmproxy抓取器启动失败。")
            return False
        
        self.logger.info("抓取器已在后台启动，现在开始执行微信UI自动化...")
        
        # 调用UI自动化发送并点击链接
        success = self.automation.send_and_open_latest_link(target_url)
        
        if not success:
            self.logger.error("UI自动化操作失败，未能成功点击链接。")
            self.cookie_reader.stop_cookie_extractor()
            return False
            
        self.logger.info("UI自动化操作成功，等待Cookie被抓取...")
        
        # 等待mitmproxy抓取到新的cookie
        if self.cookie_reader.wait_for_new_cookie(timeout=60):
            self.logger.info("✅ 成功抓取到新的Cookie！")
            self.cookie_reader.stop_cookie_extractor()
            return True
        else:
            self.logger.error("❌ Cookie抓取超时或失败。")
            self.cookie_reader.stop_cookie_extractor()
            return False

    def open_wechat_and_trigger_url(self) -> bool:
        """
        仅执行UI自动化部分：打开微信，发送并点击链接以触发mitmproxy抓取。
        不包含启动或停止mitmproxy的逻辑。
        """
        target_url = self._get_target_url_from_excel()
        if not target_url:
            return False

        self.logger.info("正在执行微信UI自动化，发送并打开链接...")
        success = self.automation.send_and_open_latest_link(target_url)
        
        if not success:
            self.logger.error("UI自动化操作失败，未能成功点击链接。")
            return False
            
        self.logger.info("UI自动化操作成功，链接已在微信内置浏览器中打开。")
        return True

    def auto_crawl_from_excel(self):
        """
        【旧版】执行从Excel启动的全自动爬取流程。
        此方法现在主要用于手动模式或调试。新的自动化流程请使用 AutomatedCrawler。
        """
        self.logger.warning("正在运行旧版的 auto_crawl_from_excel 流程。推荐使用新的 AutomatedCrawler。")
        self.logger.info("="*20 + " 步骤1: 自动获取最新Cookie " + "="*20)
        if not self._get_new_cookie_via_automation():
            self.logger.error("获取Cookie失败，无法继续执行爬取任务。")
            return

        self.logger.info("="*20 + " 步骤2: 使用新Cookie批量爬取文章 " + "="*20)
        try:
            # 使用get_latest_cookies获取解析后的数据
            cookie_data = self.cookie_reader.get_latest_cookies()
            if not cookie_data:
                self.logger.error("未能从文件中解析出有效的Cookie数据。")
                return

            # 将解析出的数据传递给爬虫
            self.spider.biz = cookie_data['biz']
            self.spider.appmsg_token = cookie_data['appmsg_token']
            self.spider.cookie = cookie_data['cookie_str']
            self.spider.headers['Cookie'] = cookie_data['cookie_str']
            
            self.spider.batch_crawl_readnum(max_pages=3, days_back=7)
            
            if self.spider.articles_data:
                self.spider.print_summary()
                excel_file = self.spider.save_to_excel()
                json_file = self.spider.save_to_json()
                self.logger.info(f"🎉 抓取完成！结果已保存到 {excel_file} 和 {json_file}")
            else:
                self.logger.warning("未获取到任何文章数据。")

        except Exception as e:
            self.logger.error(f"批量抓取过程中发生错误: {e}")
2025-08-04 00:32:27,359 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 00:32:27,360 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 00:32:27,361 - INFO - ================================================================================
2025-08-04 00:32:27,361 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-04 00:32:27,361 - INFO - ================================================================================
2025-08-04 00:32:27,362 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-04 00:32:27,372 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 00:32:27,372 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 00:32:27,372 - INFO - === 开始重置网络状态 ===
2025-08-04 00:32:27,372 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 00:32:27,572 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 00:32:27,573 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 00:32:27,573 - ERROR - 关闭代理失败: module 'winreg' has no attribute 'REG_WRITE'
2025-08-04 00:32:27,573 - INFO - ✅ 但已尝试 代理关闭操作
2025-08-04 00:32:27,574 - INFO - 🔗 正在验证网络连接...
2025-08-04 00:32:28,780 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 00:32:28,781 - INFO - ✅ 网络状态重置验证完成
2025-08-04 00:32:28,781 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 00:32:28,782 - INFO - 已备份原始代理设置: {'enable': False, 'server': '127.0.0.1:8080'}
2025-08-04 00:32:28,782 - INFO - 步骤3: 正在启动命令: mitmdump -s E:\mynj\wechat_spider\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 00:32:29,964 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1 binary
Python:    3.13.3
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-10-10.0.19045-SP0
2025-08-04 00:32:30,007 - INFO - 🔄 Cookie抓取器进程已启动，PID: 10996
2025-08-04 00:32:30,007 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 00:32:33,012 - INFO - 等待代理服务启动...
2025-08-04 00:32:33,595 - INFO - 代理服务已启动并正常工作
2025-08-04 00:32:33,595 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 10996)
2025-08-04 00:32:33,596 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 00:32:33,596 - INFO - [步骤 2/5] 正在启动 UI 自动化以触发 Cookie 获取...
2025-08-04 00:32:33,597 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 00:32:33,598 - INFO - 正在从 target_articles.xlsx 读取目标URL...
2025-08-04 00:32:34,251 - INFO - 成功读取到目标URL: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 00:32:34,251 - INFO - 正在执行微信UI自动化，发送并打开链接...
2025-08-04 00:32:34,252 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 00:32:34,252 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 00:32:34,252 - INFO - 正在查找微信主窗口...
2025-08-04 00:32:35,701 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 00:32:35,701 - INFO - 正在激活微信窗口...
2025-08-04 00:32:38,254 - INFO - 微信窗口已激活。
2025-08-04 00:32:38,254 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 00:32:45,166 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 00:32:45,166 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 00:32:48,588 - INFO - 正在查找聊天输入框...
2025-08-04 00:32:50,600 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 00:32:50,607 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 00:32:50,607 - INFO - 点击聊天输入区域坐标: (1342, 824)
2025-08-04 00:32:52,229 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-04 00:32:54,592 - INFO - 链接已粘贴，正在发送...
2025-08-04 00:32:55,233 - INFO - 找到发送按钮，点击发送...
2025-08-04 00:32:56,150 - INFO - 链接已发送。
2025-08-04 00:32:59,157 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 00:33:01,393 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 00:33:02,225 - INFO - ✅ 成功点击最新链接。
2025-08-04 00:33:02,225 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 00:33:05,225 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 00:33:05,225 - INFO - 正在查找微信浏览器窗口...
2025-08-04 00:33:05,226 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 00:33:05,233 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 00:33:08,009 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 00:33:09,036 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 00:33:09,037 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 00:33:11,549 - INFO - 第 1 次刷新完成
2025-08-04 00:33:13,060 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 00:33:13,060 - INFO - 正在查找微信浏览器窗口...
2025-08-04 00:33:13,061 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 00:33:13,069 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 00:33:15,824 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 00:33:16,854 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 00:33:16,855 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 00:33:19,365 - INFO - 第 2 次刷新完成
2025-08-04 00:33:20,876 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 00:33:20,876 - INFO - 正在查找微信浏览器窗口...
2025-08-04 00:33:20,877 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 00:33:20,884 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 00:33:23,663 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 00:33:24,692 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 00:33:24,693 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 00:33:27,208 - INFO - 第 3 次刷新完成
2025-08-04 00:33:27,209 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 00:33:27,213 - INFO - UI自动化操作成功，链接已在微信内置浏览器中打开。
2025-08-04 00:33:27,213 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 00:33:27,214 - INFO - [步骤 3/5] 正在等待 Cookie 数据写入...
2025-08-04 00:33:27,214 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)

2025-08-04 09:38:07,367 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 09:38:07,367 - INFO - ================================================================================
2025-08-04 09:38:07,367 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-04 09:38:07,367 - INFO - ================================================================================
2025-08-04 09:38:07,367 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-04 09:38:07,367 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 09:38:07,368 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 09:38:07,368 - INFO - === 开始重置网络状态 ===
2025-08-04 09:38:07,368 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 09:38:07,457 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 09:38:07,458 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 09:38:07,458 - ERROR - 关闭代理失败: module 'winreg' has no attribute 'REG_WRITE'
2025-08-04 09:38:07,458 - INFO - ✅ 但已尝试 代理关闭操作
2025-08-04 09:38:07,458 - INFO - 🔗 正在验证网络连接...
2025-08-04 09:38:08,397 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 09:38:08,398 - INFO - ✅ 网络状态重置验证完成
2025-08-04 09:38:08,398 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 09:38:08,399 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 09:38:08,400 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 09:38:11,570 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 09:38:11,571 - INFO - 🔄 Cookie抓取器进程已启动，PID: 5040
2025-08-04 09:38:11,571 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 09:38:14,572 - INFO - 等待代理服务启动...
2025-08-04 09:38:14,997 - INFO - 代理服务已启动并正常工作
2025-08-04 09:38:14,997 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 5040)
2025-08-04 09:38:14,998 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 09:38:14,998 - INFO - [步骤 2/5] 正在启动 UI 自动化以触发 Cookie 获取...
2025-08-04 09:38:14,998 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 09:38:14,999 - INFO - 正在从 target_articles.xlsx 读取目标URL...
2025-08-04 09:38:15,498 - INFO - 成功读取到目标URL: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 09:38:15,499 - INFO - 正在执行微信UI自动化，发送并打开链接...
2025-08-04 09:38:15,499 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 09:38:15,499 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 09:38:15,500 - INFO - 正在查找微信主窗口...
2025-08-04 09:38:15,862 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 09:38:15,862 - INFO - 正在激活微信窗口...
2025-08-04 09:38:18,380 - INFO - 微信窗口已激活。
2025-08-04 09:38:18,381 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 09:38:25,224 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 09:38:25,224 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 09:38:28,552 - INFO - 正在查找聊天输入框...
2025-08-04 09:38:30,553 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 09:38:30,564 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 09:38:30,564 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 09:38:32,148 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-04 09:38:34,436 - INFO - 链接已粘贴，正在发送...
2025-08-04 09:38:34,537 - INFO - 找到发送按钮，点击发送...
2025-08-04 09:38:35,340 - INFO - 链接已发送。
2025-08-04 09:38:38,341 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 09:38:40,427 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 09:38:41,272 - INFO - ✅ 成功点击最新链接。
2025-08-04 09:38:41,273 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 09:38:44,273 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 09:38:44,274 - INFO - 正在查找微信浏览器窗口...
2025-08-04 09:38:44,274 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 09:38:44,281 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 09:38:46,988 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 09:38:47,991 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 09:38:47,992 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 09:38:50,493 - INFO - 第 1 次刷新完成
2025-08-04 09:38:51,994 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 09:38:51,995 - INFO - 正在查找微信浏览器窗口...
2025-08-04 09:38:51,996 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 09:38:52,006 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 09:38:54,712 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 09:38:55,714 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 09:38:55,715 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 09:38:58,215 - INFO - 第 2 次刷新完成
2025-08-04 09:38:59,716 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 09:38:59,717 - INFO - 正在查找微信浏览器窗口...
2025-08-04 09:38:59,718 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 09:38:59,729 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 09:39:02,434 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 09:39:03,438 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 09:39:03,438 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 09:39:05,939 - INFO - 第 3 次刷新完成
2025-08-04 09:39:05,940 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 09:39:05,941 - INFO - UI自动化操作成功，链接已在微信内置浏览器中打开。
2025-08-04 09:39:05,941 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 09:39:05,942 - INFO - [步骤 3/5] 正在等待 Cookie 数据写入...
2025-08-04 09:39:05,942 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 09:41:05,985 - ERROR - 等待Cookie超时！
2025-08-04 09:41:05,985 - ERROR - ❌ 等待 Cookie 超时，流程中止。
2025-08-04 09:41:05,986 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 09:41:05,986 - INFO - 正在停止Cookie抓取器 (PID: 5040)...
2025-08-04 09:41:05,986 - INFO - Cookie抓取器已成功终止。
2025-08-04 09:41:05,987 - INFO - 正在验证并清理代理设置...
2025-08-04 09:41:05,987 - INFO - === 开始重置网络状态 ===
2025-08-04 09:41:05,987 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 09:41:06,152 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 09:41:06,152 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 09:41:06,152 - ERROR - 关闭代理失败: module 'winreg' has no attribute 'REG_WRITE'
2025-08-04 09:41:06,152 - INFO - ✅ 但已尝试 代理关闭操作
2025-08-04 09:41:06,152 - INFO - 🔗 正在验证网络连接...
2025-08-04 09:41:08,189 - ERROR - ❌ 网络连接异常: HTTPSConnectionPool(host='httpbin.org', port=443): Max retries exceeded with url: /ip (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023E85AFC7D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-08-04 09:41:08,189 - INFO - 验证中: 代理状态=True, 网络状态=False
2025-08-04 09:41:08,189 - INFO - 🔄 简要重试检查 1/2
2025-08-04 09:41:10,246 - ERROR - ❌ 网络连接异常: HTTPSConnectionPool(host='httpbin.org', port=443): Max retries exceeded with url: /ip (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023E85AFCCD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-08-04 09:41:10,246 - INFO - 验证中: 代理状态=True, 网络状态=False
2025-08-04 09:41:10,246 - INFO - ℹ️ 网络重置流程已完成，代理清理已执行
2025-08-04 09:41:10,246 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 09:41:12,306 - ERROR - ❌ 网络连接异常: HTTPSConnectionPool(host='httpbin.org', port=443): Max retries exceeded with url: /ip (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000023E85AFCF50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-08-04 09:41:12,306 - WARNING - ⚠️ 网络连接验证失败，可能需要手动检查

2025-08-04 10:58:59,901 - INFO - 检测到 --auto 参数或无参数，启动全新自动化爬取流程...
2025-08-04 10:58:59,901 - INFO - ================================================================================
2025-08-04 10:58:59,901 - INFO - 🚀 全新自动化流程启动 🚀
2025-08-04 10:58:59,901 - INFO - ================================================================================
2025-08-04 10:58:59,901 - INFO - [步骤 1/5] 正在启动 Cookie 抓取器 (mitmdump)...
2025-08-04 10:58:59,901 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-04 10:58:59,903 - INFO - 步骤1: 正在准备网络环境...
2025-08-04 10:58:59,903 - INFO - === 开始重置网络状态 ===
2025-08-04 10:58:59,903 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 10:58:59,982 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 10:58:59,983 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 10:58:59,983 - INFO - 系统代理已成功关闭
2025-08-04 10:58:59,983 - INFO - ✅ 代理关闭操作
2025-08-04 10:58:59,983 - INFO - 🔗 正在验证网络连接...
2025-08-04 10:59:00,858 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 10:59:00,859 - INFO - ✅ 网络状态重置验证完成
2025-08-04 10:59:00,860 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-04 10:59:00,861 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-04 10:59:00,862 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider2\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-04 10:59:01,330 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-04 10:59:01,331 - INFO - 🔄 Cookie抓取器进程已启动，PID: 19868
2025-08-04 10:59:01,331 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-04 10:59:04,331 - INFO - 等待代理服务启动...
2025-08-04 10:59:04,749 - INFO - 代理服务已启动并正常工作
2025-08-04 10:59:04,749 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 19868)
2025-08-04 10:59:04,750 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-04 10:59:04,750 - INFO - [步骤 2/5] 正在启动 UI 自动化以触发 Cookie 获取...
2025-08-04 10:59:04,750 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-04 10:59:04,751 - INFO - 正在从 target_articles.xlsx 读取目标URL...
2025-08-04 10:59:04,826 - INFO - 成功读取到目标URL: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-04 10:59:04,826 - INFO - 正在执行微信UI自动化，发送并打开链接...
2025-08-04 10:59:04,826 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-04 10:59:04,826 - INFO - 准备将链接发送到文件传输助手...
2025-08-04 10:59:04,826 - INFO - 正在查找微信主窗口...
2025-08-04 10:59:04,923 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-04 10:59:04,923 - INFO - 正在激活微信窗口...
2025-08-04 10:59:07,436 - INFO - 微信窗口已激活。
2025-08-04 10:59:07,437 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-04 10:59:14,192 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-04 10:59:14,193 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-04 10:59:17,527 - INFO - 正在查找聊天输入框...
2025-08-04 10:59:19,528 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-04 10:59:19,538 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-04 10:59:19,538 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-04 10:59:21,140 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-04 10:59:23,427 - INFO - 链接已粘贴，正在发送...
2025-08-04 10:59:23,534 - INFO - 找到发送按钮，点击发送...
2025-08-04 10:59:24,360 - INFO - 链接已发送。
2025-08-04 10:59:27,361 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-04 10:59:29,442 - INFO - 已定位到最新的消息项，准备点击。
2025-08-04 10:59:30,176 - INFO - ✅ 成功点击最新链接。
2025-08-04 10:59:30,176 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-04 10:59:33,177 - INFO - 正在执行第 1 次刷新操作...
2025-08-04 10:59:33,177 - INFO - 正在查找微信浏览器窗口...
2025-08-04 10:59:33,178 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 10:59:33,186 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-04 10:59:35,907 - INFO - 已成功激活焦点浏览器窗口
2025-08-04 10:59:36,908 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 10:59:36,909 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 10:59:39,410 - INFO - 第 1 次刷新完成
2025-08-04 10:59:40,911 - INFO - 正在执行第 2 次刷新操作...
2025-08-04 10:59:40,912 - INFO - 正在查找微信浏览器窗口...
2025-08-04 10:59:40,912 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 10:59:40,915 - INFO - 尝试传统窗口查找方法...
2025-08-04 10:59:43,833 - INFO - 找到浏览器窗口: {'Name': '微信'}
2025-08-04 10:59:45,650 - INFO - 已成功激活浏览器窗口
2025-08-04 10:59:46,652 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 10:59:46,653 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 10:59:49,153 - INFO - 第 2 次刷新完成
2025-08-04 10:59:50,655 - INFO - 正在执行第 3 次刷新操作...
2025-08-04 10:59:50,656 - INFO - 正在查找微信浏览器窗口...
2025-08-04 10:59:50,656 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-04 10:59:50,659 - INFO - 尝试传统窗口查找方法...
2025-08-04 10:59:53,501 - INFO - 找到浏览器窗口: {'Name': '微信'}
2025-08-04 10:59:55,317 - INFO - 已成功激活浏览器窗口
2025-08-04 10:59:56,319 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-04 10:59:56,320 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-04 10:59:58,821 - INFO - 第 3 次刷新完成
2025-08-04 10:59:58,822 - INFO - ✅ 自动刷新操作全部完成
2025-08-04 10:59:58,822 - INFO - UI自动化操作成功，链接已在微信内置浏览器中打开。
2025-08-04 10:59:58,823 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-04 10:59:58,823 - INFO - [步骤 3/5] 正在等待 Cookie 数据写入...
2025-08-04 10:59:58,824 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-04 10:59:59,824 - INFO - 检测到Cookie文件已生成。
2025-08-04 10:59:59,839 - INFO - 从文件中解析到有效Cookie数据。
2025-08-04 10:59:59,840 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-04 10:59:59,840 - INFO - [步骤 4/5] 正在停止 Cookie 抓取器并关闭系统代理...
2025-08-04 10:59:59,840 - INFO - 🧹 开始清理抓取器资源...
2025-08-04 10:59:59,841 - INFO - 正在停止Cookie抓取器 (PID: 19868)...
2025-08-04 10:59:59,842 - INFO - Cookie抓取器已成功终止。
2025-08-04 10:59:59,843 - INFO - 正在验证并清理代理设置...
2025-08-04 10:59:59,843 - INFO - === 开始重置网络状态 ===
2025-08-04 10:59:59,843 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-04 10:59:59,924 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-04 10:59:59,924 - INFO - 🔧 正在关闭系统代理设置...
2025-08-04 10:59:59,924 - INFO - 系统代理已成功关闭
2025-08-04 10:59:59,924 - INFO - ✅ 代理关闭操作
2025-08-04 10:59:59,924 - INFO - 🔗 正在验证网络连接...
2025-08-04 11:00:01,850 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 11:00:01,850 - INFO - ✅ 网络状态重置验证完成
2025-08-04 11:00:01,850 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-04 11:00:03,665 - INFO - ✅ 网络连接正常（无代理）
2025-08-04 11:00:03,666 - INFO - ✅ 网络连接验证正常
2025-08-04 11:00:06,666 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-04 11:00:06,667 - INFO - [步骤 5/5] 正在启动批量文章爬取...
